// lion ui import
import { Box, Typography } from 'lion-ui'
import { UserProfile } from '@constants/userProfile'

// Props imports
import type TooltipContentProps from './TooltipContent.props'

// Component imports
import PmIcon from '@components/Icons/PmIcon/PmIcon'
import { useReactiveVar } from '@apollo/client'
import { userProfileVar } from '@gql/client/localState'
import CrossRowIcon from '@components/Icons/CrossRowIcon/CrossRowIcon'
import { TaskStates } from '@constants/taskStatus'
import type React from 'react'

const TooltipContent = ({ assignment }: TooltipContentProps): React.JSX.Element => {
  const userProfile = useReactiveVar(userProfileVar)
  const isLightUser = userProfile === UserProfile.LIGHT_USER
  const assignmentIcaStatus = assignment.assignmentIcaStatus

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <PmIcon width="16" height="16" />
        <Typography sx={{ fontSize: '0.75rem' }}>
          <b>Project Manager:</b> {assignment.projectManagerName}
        </Typography>
      </Box>

      <Typography sx={{ fontSize: '0.75rem' }}>
        <b>Project:</b> {assignment.projectName}
      </Typography>
      <Typography sx={{ fontSize: '0.75rem' }}>
        <b>Task:</b> {assignment.taskName}
      </Typography>
      <Typography sx={{ fontSize: '0.75rem' }}>
        <b>Task Status:</b>{' '}
        {assignment.taskState?.toLowerCase() === 'external'
          ? (TaskStates as Record<string, string>)[assignment.assignmentState?.toLowerCase()] ||
            assignment.assignmentState
          : (TaskStates as Record<string, string>)[assignment.taskState?.toLowerCase()] || assignment.taskState}
      </Typography>
      <Typography sx={{ fontSize: '0.75rem' }}>
        <b>Job Number:</b> {assignment.altairJobNumber ?? 'N/A'}
      </Typography>
      <hr style={{ opacity: '0.4' }} />

      {assignment.isPlaceholder && (
        <>
          <Typography sx={{ fontSize: '0.75rem' }}>
            <b>Agency:</b> {assignment.agencyCode} : {assignment.agencyName}
          </Typography>
          <Typography sx={{ fontSize: '0.75rem' }}>
            <b>Location:</b> {assignment.locationName}
          </Typography>
          <Typography sx={{ fontSize: '0.75rem' }}>
            <b>Cost Center:</b> {assignment.costCenterName}
          </Typography>
        </>
      )}
      {!assignment.isPlaceholder && (
        <Typography sx={{ fontSize: '0.75rem' }}>
          <b>Agency:</b> {assignment.projectAgencyCode} : {assignment.projectAgencyName}
        </Typography>
      )}

      {!isLightUser && assignmentIcaStatus && (
        <>
          <hr style={{ opacity: '0.4' }} />
          <Box sx={{ display: 'flex', gap: 1 }}>
            <CrossRowIcon height="16" width="16" fillColor="#FFFFFF" />
            <Typography sx={{ fontSize: '0.75rem' }}>
              <b>ICA Status:</b> {assignmentIcaStatus}
            </Typography>
          </Box>
        </>
      )}
    </Box>
  )
}

export default TooltipContent
