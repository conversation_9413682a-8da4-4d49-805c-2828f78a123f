// React imports
import { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from 'react'
import dayjs from 'dayjs'

// Lion ui imports
import { Box } from 'lion-ui'

// Gridstack imports
import { type GridItemHTMLElement } from 'gridstack'

// Constants imports
import { numsOfDaysPerWeek } from '@constants/timeOptions'

// Component imports
import CalendarDragItem from '@components/Calendar/CalendarDragItem/CalendarDragItem'
import CalendarUtilization from '@components/Calendar/CalendarUtilization/CalendarUtilization'
import { useScroll } from '../../../context/scrollContext'

// Apollo imports
import { gql, useApolloClient, useMutation, useReactiveVar, useQuery, type ApolloError } from '@apollo/client'
import {
  numOfWeeksVar,
  renderKeyVar,
  resourceDetailsVar,
  showAlertVar,
  showAllocationDetailsFormVar,
  sourceAssignmentVar,
  userInfoVar,
  resourceTypeVar
} from '@gql/client/localState'

// GraphQL imports
import { assignmentFieldsQuery, ASSIGNMENTS_QUERY } from '@gql/queries/assignments.query'
import { UPDATE_ASSIGNMENT_MUTATION } from '@gql/mutations/assignment.mutation'
import { ORG_STRUCTURE_AGENCY_COST_CENTER_QUERY, RESOURCE_ORG_STRUCTURE_QUERY } from '@gql/queries/resources.query'

// Type imports
import type { GridStackEvent, GridStackNode, ItemDropped, DragRowType } from '@typeDefs/Gridstack'
import { type UpdateAssignmentParams, type Assignment } from '@typeDefs/Assignments'
import { type ResourceOrgStructure } from '@typeDefs/Resource'

// Utils imports
import convertGridPositionToDates from '@utils/convertGridPositionToDates'
import { AssignmentStatus } from '@constants/taskStatus'
import { TileSizeConvert } from '@constants/tileSizeConvert'
import { gplError } from '@utils/gplError'
import { isLoadingAssignmentVar, updateAssignmentIdVar } from '@gql/client/localState/assignments'
import getLocaleDateFormat from '@utils/getLocaleDateFormat'

const CalendarDrag = ({ id, assignments, resource, type }: DragRowType) => {
  const currentColumns = useReactiveVar(numOfWeeksVar) * numsOfDaysPerWeek
  const dragContainer = useRef<HTMLDivElement | null>(null)
  const rowId = `gridStack${id}`
  const externalLink = useMemo(() => process.env.WF_SERVER_URL, [])
  const { setDragging } = useScroll()

  const client = useApolloClient()
  const isLoading = isLoadingAssignmentVar()
  const updateAssignmentId = useReactiveVar(updateAssignmentIdVar)
  const activeUser = useReactiveVar(userInfoVar)

  const [shouldRerenderKey, setShouldRerenderKey] = useState(Date.now())

  // Cache the assignments in a map for faster lookup
  const assignmentsMap = useMemo(() => {
    const map = new Map()
    assignments.forEach((assignment) => map.set(assignment.id, assignment))
    return map
  }, [assignments])

  const opts = useMemo(
    () => ({
      minRow: 24,
      acceptWidgets: true,
      column: currentColumns,
      float: true,
      cellHeight: 5,
      margin: '4px 2px 0px 2px', // could be caused another issue with the gridstack, It was changed by Vikash request
      resizable: { handles: 'e, s, w, sw, se' }
    }),
    [currentColumns]
  )

  const { error: resourceOrgStructureError, refetch: resourceOrgStructureRefetch } = useQuery<{
    getResourceOrgStructure?: ResourceOrgStructure
  }>(RESOURCE_ORG_STRUCTURE_QUERY, {
    onError: (error) => {
      gplError(error, resourceOrgStructureError)
    },
    skip: true
  })

  const handleAssignmentResize = useCallback(
    (event: { target: { dataset: { gsId: string; gsH: number } } }, element: GridItemHTMLElement) => {
      const id = event.target.dataset.gsId
      const gridstackNode = element.gridstackNode

      // update the rendered hours
      const assignment = assignmentsMap.get(id)
      if (assignment && gridstackNode) {
        const hoursElement = document.getElementById(`hours-${assignment.userId}-${assignment.id}`)
        if (hoursElement) {
          if (gridstackNode.h !== undefined) {
            hoursElement.innerHTML = `${gridstackNode.h / TileSizeConvert.HEIGHT_TO_TIME}h`
          }
        }
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [activeUser.altairNumber]
  )

  const [updateAssignmentMutation] = useMutation(UPDATE_ASSIGNMENT_MUTATION, {
    refetchQueries: [ASSIGNMENTS_QUERY],
    awaitRefetchQueries: true,
    onCompleted: () => {
      isLoadingAssignmentVar(false)
      renderKeyVar(Date.now())
    },
    onError: () => {
      isLoadingAssignmentVar(false)
    }
  })

  const { error: orgDataError, refetch: refetchOrgStructureAgencyCostCenter } = useQuery(
    ORG_STRUCTURE_AGENCY_COST_CENTER_QUERY,
    {
      variables: {},
      onError: (error) => {
        gplError(error, orgDataError)
      },
      skip: true
    }
  )

  const checkIfAssignmentIsBeyondTaskDates = useCallback(
    ({ itemDropped, sourceAssignmentStored }: { itemDropped: ItemDropped; sourceAssignmentStored: Assignment }) => {
      const itemDroppedDate = convertGridPositionToDates(itemDropped.x, itemDropped.w)
      if (
        (sourceAssignmentStored &&
          dayjs(getLocaleDateFormat(sourceAssignmentStored.taskDueDate ?? '')).toDate() <
            dayjs(itemDroppedDate.endDate).toDate()) ||
        (sourceAssignmentStored &&
          dayjs(getLocaleDateFormat(sourceAssignmentStored.taskStartDate ?? '')).toDate() >
            dayjs(itemDroppedDate.startDate).toDate())
      ) {
        return true
      }
      return false
    },
    []
  )

  const alertIfAssignmentExceedsTaskDates = useCallback(
    ({ itemDropped, sourceAssignmentStored }: { itemDropped: ItemDropped; sourceAssignmentStored: Assignment }) => {
      const itemDroppedDate = convertGridPositionToDates(itemDropped.x, itemDropped.w)
      const isAfterOrBefore = dayjs(itemDroppedDate.endDate).isAfter(sourceAssignmentStored.taskDueDate)
      const dateType = isAfterOrBefore ? 'due date' : 'start date'
      const formattedDate = isAfterOrBefore
        ? dayjs(sourceAssignmentStored.taskDueDate).format('MM/DD/YYYY')
        : dayjs(sourceAssignmentStored.taskStartDate).format('MM/DD/YYYY')

      const htmlMessage = `
        <span>
          You are exceeding the ${dateType} (${formattedDate}) of the task.
          <b>
        <a href="${externalLink}/task/${sourceAssignmentStored.taskId}/overview" target="_blank">
          Click here
        </a>
          </b>
          to change the ${dateType} of the task.
        </span>
      `
      showAlertVar({ show: true, message: htmlMessage })
      setShouldRerenderKey(Date.now())
      isLoadingAssignmentVar(false)
    },
    [externalLink]
  )

  const handleUpdateAssignmentMutation = useCallback(
    (params: { assignment: UpdateAssignmentParams }) => {
      updateAssignmentMutation({ variables: { ...params } }).catch((error: Error) => {
        isLoadingAssignmentVar(false)
        showAlertVar({ show: true, message: `${error.message}` })
      })
    },
    [updateAssignmentMutation]
  )

  const handleResourceOrgStructureRefetch = useCallback(
    (resourceIndex: string, params: { assignment: UpdateAssignmentParams }) => {
      resourceOrgStructureRefetch({ resourceId: resourceIndex })
        .then(({ data: { getResourceOrgStructure } }: { data: { getResourceOrgStructure?: ResourceOrgStructure } }) => {
          params.assignment = {
            ...params.assignment,
            agencyName: getResourceOrgStructure?.agencyName ?? '',
            locationName: getResourceOrgStructure?.locationName ?? '',
            costCenterName: getResourceOrgStructure?.costCenterName ?? '',
            agencyCode: getResourceOrgStructure?.agencyCode ?? '',
            costCenterCode: getResourceOrgStructure?.costCenterCode ?? ''
          }
          handleUpdateAssignmentMutation(params)
        })
        .catch((error) => {
          showAlertVar({ show: true, message: `${error.message}` })
        })
    },
    [resourceOrgStructureRefetch, handleUpdateAssignmentMutation]
  )

  const handleAssignmentUpdate = useCallback(
    (itemDropped: ItemDropped) => {
      const assignmentId = itemDropped.el.dataset.gsId
      const resourceIndex = itemDropped.el.offsetParent?.dataset.index ?? '0'
      const sourceAssignmentStored = sourceAssignmentVar()
      const isPlaceHolder = type === 'placeholder'
      isLoadingAssignmentVar(true)

      const updateAssignment = (
        resourceIndex: string,
        update: { x: number; y: number; width: number; height: number; id: string }
      ) => {
        const dragContainer = document.getElementById('dragContainer')

        if (dragContainer) {
          dragContainer.innerHTML = ''
        }
        const { startDate, endDate } = convertGridPositionToDates(update.x, update.width)

        const params: { assignment: UpdateAssignmentParams } = {
          assignment: {
            externalId: update.id,
            startDate,
            dueDate: endDate,
            userId: resourceIndex,
            hoursPerDay: update.height / TileSizeConvert.HEIGHT_TO_TIME,
            userLoggedInExternalId: activeUser.altairNumber,
            updateAssignmentDate: dayjs().toISOString(),
            isSplit: false
          }
        }

        if (sourceAssignmentStored) {
          if (sourceAssignmentStored.id !== assignmentId) {
            // Skip updating the assignment if the IDs do not match
            return
          }

          // If the user exists, it indicates that this is a drag-and-drop operation
          if (resourceIndex !== sourceAssignmentStored.userId) {
            if (!isPlaceHolder) {
              handleResourceOrgStructureRefetch(resourceIndex, params)
            } else {
              // Target is a placeholder
              params.assignment = {
                ...params.assignment,
                agencyName: sourceAssignmentStored.agencyName,
                locationName: sourceAssignmentStored.locationName,
                costCenterName: sourceAssignmentStored.costCenterName,
                agencyCode: sourceAssignmentStored.agencyCode,
                costCenterCode: sourceAssignmentStored.costCenterCode
              }
              // Assignment data already exists
              if (sourceAssignmentStored.agencyCode && sourceAssignmentStored.costCenterCode) {
                handleUpdateAssignmentMutation(params)
              } else {
                // Use the logged-in user's data for assignment updates
                handleResourceOrgStructureRefetch(activeUser?.altairNumber, params)
              }
            }
          } else {
            handleUpdateAssignmentMutation(params)
          }
        }
      }

      // If the assignment is moved to a different resource, update the Apollo cache to prevent flickering
      if (sourceAssignmentStored && resourceIndex !== sourceAssignmentStored.userId) {
        // update apollo cache with the updated assignment
        const ASSIGNMENT_FRAGMENT = gql`
          fragment AssignmentFragment on Assignment {
            ${assignmentFieldsQuery}
          }`
        const assignmentData = client.readFragment({
          id: `Assignment:${sourceAssignmentStored.id}`,
          fragment: ASSIGNMENT_FRAGMENT
        })

        if (assignmentData) {
          const updatedAssignment = {
            ...assignmentData,
            userId: resourceIndex,
            x: itemDropped.x,
            startDate: convertGridPositionToDates(itemDropped.x, itemDropped.w).startDate,
            dueDate: convertGridPositionToDates(itemDropped.x, itemDropped.w).endDate,
            isPlaceholder: isPlaceHolder
          }

          client.writeFragment({
            id: `Assignment:${updatedAssignment.id}`,
            fragment: ASSIGNMENT_FRAGMENT,
            data: updatedAssignment
          })

          updateAssignmentIdVar(assignmentId)
          renderKeyVar(Date.now())
        }
      }

      // not updating the assignment if is beyond task dates.
      if (sourceAssignmentStored && checkIfAssignmentIsBeyondTaskDates({ itemDropped, sourceAssignmentStored })) {
        alertIfAssignmentExceedsTaskDates({ itemDropped, sourceAssignmentStored })
        return
      }

      updateAssignment(resourceIndex, {
        x: itemDropped.x,
        y: itemDropped.y,
        width: itemDropped.w,
        height: itemDropped.h,
        id: assignmentId
      })
    },
    [
      checkIfAssignmentIsBeyondTaskDates,
      handleResourceOrgStructureRefetch,
      handleUpdateAssignmentMutation,
      client,
      alertIfAssignmentExceedsTaskDates,
      type,
      activeUser.altairNumber
    ]
  )

  useEffect(() => {
    const gridContainer = document.getElementById(rowId)
    dragContainer.current = document.getElementById('dragContainer') as HTMLDivElement

    if (!gridContainer) {
      return
    }

    // @ts-expect-error : GridStack is mounted in the browser after virtual DOM loads
    const grid = GridStack.init(opts, gridContainer)

    grid.on('dragstart', (event: GridStackEvent, el: GridStackNode) => {
      const assignment = assignments.find((assignment) => assignment.id === el.dataset.gsId.toString())
      sourceAssignmentVar(assignment)
      setDragging(true)
      updateAssignmentIdVar(el.dataset.gsId as string)
    })

    grid.on('dropped', (a: GridStackNode, b: GridStackNode, itemDropped: ItemDropped) => {
      updateAssignmentIdVar(itemDropped.el.dataset.gsId)
      handleAssignmentUpdate(itemDropped)
      setDragging(false)
    })

    grid.on('resizestart', (event: GridStackEvent, el: GridStackNode) => {
      const assignment = assignments.find((assignment) => assignment.id === el.dataset.gsId.toString())
      sourceAssignmentVar(assignment)
    })

    grid.on('resizestop', (event: GridStackEvent, el: GridStackNode) => {
      updateAssignmentIdVar(el.dataset.gsId as string)
    })

    grid.on('resize', handleAssignmentResize)
    grid.on('dragstop', (event: GridStackEvent, el: GridStackNode) => {
      updateAssignmentIdVar(el.dataset.gsId as string)
      setDragging(false)
    })

    grid.on('change', (event: { detail: GridStackNode[] }) => {
      event.detail.forEach((itemDropped) => {
        handleAssignmentUpdate(itemDropped as unknown as ItemDropped)
      })
    })
    return () => {
      grid.off('resize', handleAssignmentResize)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [handleAssignmentResize, activeUser.altairNumber, shouldRerenderKey])

  return (
    <Box sx={{ position: 'relative' }} key={shouldRerenderKey}>
      <Box sx={{ position: 'relative', zIndex: 1, paddingBottom: '30px' }}>
        <Box id={rowId} data-index={id} className={`grid-stack gs-${currentColumns}`}>
          {assignments.map((assignment, i) => (
            <CalendarDragItem
              resourceType={type}
              assignment={assignment}
              key={assignment.id}
              isLoading={(() => {
                return updateAssignmentId === assignment.id && isLoading
              })()}
              openModal={() => {
                resourceTypeVar(type)
                if (!assignment.costCenterName && !assignment.agencyName) {
                  refetchOrgStructureAgencyCostCenter({
                    params: { agencyCode: assignment.agencyCode, costCenterCode: assignment.costCenterCode }
                  })
                    .then(({ data }) => {
                      const { costCenterName, agencyName, locationName } =
                        data.getOrgStructureByAgencyCodeCostCenterCode

                      const newAssignment = { ...assignment, agencyName, locationName, costCenterName }

                      showAllocationDetailsFormVar(newAssignment)
                    })
                    .catch((error) => {
                      gplError(error as ApolloError, orgDataError)
                    })
                }

                showAllocationDetailsFormVar(assignment)
                if (assignment.assignmentState.toLowerCase() === AssignmentStatus.REQUESTED) {
                  resourceDetailsVar(resource)
                }
              }}
            />
          ))}
        </Box>
      </Box>

      <CalendarUtilization assignments={assignments} resource={resource} />
    </Box>
  )
}

export default CalendarDrag
