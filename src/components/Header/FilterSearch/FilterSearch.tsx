// React and Hooks
import { useEffect, useRef, useState } from 'react'

// GraphQL & Queries imports
import USERS_GROUPS_QUERY from '@gql/queries/usersGroups.query'
import { useMutation, useQuery, useReactiveVar } from '@apollo/client'
import {
  activeDatesVar,
  activeFiltersVar,
  activeSortVar,
  brandIdsVar,
  userProfileVar,
  userInfoVar,
  selectedFiltersVar,
  selectedPlaceholderOrgStructureFilterVar,
  showAlertVar,
  renderKeyVar,
  placeholderSettingsSaveVar,
  placeholderSettingsPayloadVar,
  placeholderSettingsResetVar
} from '@gql/client/localState'

// Third-party Libraries
import SearchIcon from '@mui/icons-material/Search'
import { Box, Chip, Divider, Grid, IconButton, InputBase, palette, Paper, Typography } from 'lion-ui'
import Image from 'next/image'

// Local Components
import FilterFooter from '@components/Header/FilterFooter/FilterFooter'
import SimpleArrowIcon from '@components/Icons/SimpleArrowIcon/SimpleArrowIcon'
import FilterIllustrationIcon from '@components/Icons/FilterIllustrationIcon/FilterIllustrationIcon'
import FilterList from '@components/Header/FilterList/FilterList'
import PlaceholdersSettingsBtn from '@components/Header/PlaceholdersSettings/PlaceholdersSettingsBtn'
import PlaceholdersSettings from '@components/Header/PlaceholdersSettings/PlaceholdersSettings'

// Types and Constants
import { availableFilters, Filters as FilterEnum } from '@constants/filters'
import { type Filters } from '@typeDefs/Filters'
import type { UsersGroups } from '@typeDefs/UsersGroups'
import type { LabelItem, LabelItemWithSubItems } from '@typeDefs/LabelItem'
import { TaskStates } from '@constants/taskStatus'

// Utils import
import { gplError } from '@utils/gplError'
import { SelectAllDropdown } from '../FilterList/SelectAllDropdown/SelectAllDropdown'
import BRANDS_QUERY from '@gql/queries/brands.query'
import { type Brand } from '@typeDefs/Brand'
import { type ProjectType } from '@typeDefs/ProjectType'
import { UserProfile } from '@constants/userProfile'
import { PROJECTS_BY_PROJECT_MANAGER_QUERY, PROJECTS_BY_RESOURCE_MANAGER_QUERY } from '@gql/queries/projects.query'
import { RESOURCES_QUERY } from '@gql/queries/resources.query'
import ChipList from './ChipList/ChipList'
import { TASKS_BY_PM_GROUPED_BY_PROJECT_QUERY, TASKS_BY_RM_GROUPED_BY_PROJECT_QUERY } from '@gql/queries/tasks.query'
import { type Resource } from '@typeDefs/Resource'
import PlaceholdersFilter from '@components/Header/PlaceholdersFilter/PlaceholdersFilter'

// Icons import
import { createPortal } from 'react-dom'
import { PLACEHOLDER_SAVE_MUTATION } from '@gql/mutations/placeholders.mutation'
import { type PlaceholdersSettingsPayload } from '@typeDefs/PlaceholdersSettingsPayload'
import { type PlaceholderOrgStructure, type PlaceholderResponse } from '@typeDefs/Placeholders'
import { PLACEHOLDERS_QUERY } from '@gql/queries/placeholders.query'

const FilterSearch = () => {
  const activeUser = useReactiveVar(userInfoVar)
  const [isActive, setIsActive] = useState(false)
  const [inputText, setInputText] = useState('')
  const [activeFilter, setActiveFilter] = useState<Filters | null>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const [triggerUsersGroupsQuery, setTriggerUsersGroupsQuery] = useState(false)
  const [triggerResourcesQuery, setTriggerResourcesQuery] = useState(false)
  const [triggerBrandsQuery, setTriggerBrandsQuery] = useState(false)
  const [triggerProjectsQuery, setTriggerProjectsQuery] = useState(false)
  const [triggerTasksQuery, setTriggerTasksQuery] = useState(false)
  const [triggerPlaceholdersFiltersQuery, setTriggerPlaceholdersFiltersQuery] = useState(false)
  const [isListLoading, setIsListLoading] = useState(false)
  const [expandPlaceholderActivated, setExpandPlaceholderActivated] = useState(false)
  const searchInputRef = useRef<HTMLInputElement>(null)
  const menuListRef = useRef<HTMLDivElement>(null)
  const savePlaceHolders = useReactiveVar(placeholderSettingsSaveVar)
  const resetPlaceholders = useReactiveVar(placeholderSettingsResetVar)
  const [disableApplyFilters, setDisableApplyFilters] = useState(true)
  const selectedFilters = useReactiveVar(selectedFiltersVar)
  const selectedPlaceholdersOrgStructureFilters = useReactiveVar(selectedPlaceholderOrgStructureFilterVar)
  const expandedPlaceholder = useReactiveVar(placeholderSettingsSaveVar)

  const activeFilters = useReactiveVar(activeFiltersVar)
  const userProfile = useReactiveVar(userProfileVar)
  const userProfileKey: string =
    userProfile === UserProfile.PROJECT_MANAGER
      ? 'getProjectsByProjectManagerWorkCode'
      : 'getProjectsByResourceManagerId'
  const responseTaskByProfile =
    userProfile === UserProfile.PROJECT_MANAGER ? 'getPMTasksByGroupOfProjects' : 'getRMTasksByGroupOfProjects'
  const requestTaskByProfile =
    userProfile === UserProfile.PROJECT_MANAGER
      ? TASKS_BY_PM_GROUPED_BY_PROJECT_QUERY
      : TASKS_BY_RM_GROUPED_BY_PROJECT_QUERY

  const pagination = { pageNumber: 1, pageSize: 50 }
  const activeSort = useReactiveVar(activeSortVar) || { field: 'name', order: 'asc' }
  const [activeList, setActiveList] = useState<Map<string, LabelItem[] | LabelItemWithSubItems[]>>(new Map())
  const brandIds = useReactiveVar(brandIdsVar)

  const [placeholdersFiltersFormData, setPlaceholdersFiltersFormData] = useState<PlaceholderOrgStructure>({
    businessUnits: null,
    locations: null,
    costCenters: null
  })

  const handleClickOutside = (event: MouseEvent) => {
    if (
      searchInputRef.current &&
      !searchInputRef.current.contains(event.target as Node) &&
      menuListRef.current &&
      !menuListRef.current.contains(event.target as Node)
    ) {
      setIsActive(false)
    }
  }

  const { error: resourcesError, loading: resourcesDataLoading } = useQuery<{ resources: { items: Resource[] } }>(
    RESOURCES_QUERY,
    {
      variables: {
        params: {
          pageNumber: pagination.pageNumber,
          pageSize: pagination.pageSize,
          userId: activeUser?.altairNumber,
          sort: { field: activeSort.field, order: activeSort.order },
          searchName: inputText.length > 2 ? inputText : null,
          startDate: activeDatesVar()?.startDate || '',
          endDate: activeDatesVar()?.endDate || ''
        }
      },
      onError: (error) => {
        gplError(error, resourcesError)
      },
      onCompleted: (data) => {
        updateActiveList(
          FilterEnum.RESOURCES,
          data?.resources.items.map((item) => {
            const details = [item.jobTitle, item.workCode].filter(Boolean).join(' ⸱ ')
            return { name: details ? `${item.name} ⸱ ${details}` : item.name, id: item.id }
          }) ?? []
        )
      },
      fetchPolicy: 'cache-first',
      skip: !triggerResourcesQuery || !isActive
    }
  )

  const {
    data: placeholdersData,
    error: placeholdersError,
    loading: placeholdersDataLoading
  } = useQuery<{
    placeholders: PlaceholderResponse
  }>(PLACEHOLDERS_QUERY, {
    variables: {
      params: {
        userId: activeUser?.altairNumber,
        pageNumber: 1, // placeholders are not paginated, always use the first page
        pageSize: 1000, // placeholders are not paginated, always use a big number
        sort: { field: activeSort.field, order: activeSort.order }
      }
    },

    skip: !triggerPlaceholdersFiltersQuery || !isActive,
    onError: (error) => {
      gplError(error, placeholdersError)
    },
    onCompleted: (data) => {
      updateActiveList(
        FilterEnum.PLACEHOLDERS,
        data?.placeholders?.items.map((x: { name: string; id: string }) => ({ name: x.name, id: x.id })) ?? []
      )
    },
    fetchPolicy: 'network-only'
  })

  const {
    data: usersGroupsData,
    error: usersGroupsError,
    loading: usersGroupsDataLoading
  } = useQuery<{ usersGroups: UsersGroups[] }>(USERS_GROUPS_QUERY, {
    variables: { params: { resourceManagerId: activeUser?.altairNumber, pageNumber: 1, pageSize: 10 } },
    onCompleted: (data) => {
      updateActiveList(
        FilterEnum.GROUPS,
        data?.usersGroups.map((item) => ({ name: item.name, id: item.id.toString() })) ?? []
      )
    },
    onError: (error) => {
      gplError(error, usersGroupsError)
    },
    fetchPolicy: 'network-only',
    skip: !triggerUsersGroupsQuery || !isActive
  })

  const {
    error: projectsError,
    data: projects,
    loading: projectsDataLoading
  } = useQuery<ProjectType>(
    userProfile === UserProfile.PROJECT_MANAGER
      ? PROJECTS_BY_PROJECT_MANAGER_QUERY
      : PROJECTS_BY_RESOURCE_MANAGER_QUERY,
    {
      variables: {
        workCode: activeUser?.altairNumber,
        isExpandedPlaceholderApplied:
          (userProfile === UserProfile.RESOURCE_MANAGER && savePlaceHolders) || expandedPlaceholder
      },
      onError: (error) => {
        gplError(error, projectsError)
      },
      onCompleted: (data) => {
        const brandIds = data[userProfileKey]?.map((item) => item.brandId).filter((item) => item) ?? []
        brandIdsVar([...new Set(brandIds)])
        if (activeFilters?.brands?.length > 0) {
          const filteredProjectsByBrands = data[userProfileKey].filter((x) => {
            return activeFilters?.brands?.some((p) => p.id === x.brandId)
          })
          updateActiveList(FilterEnum.PROJECTS, filteredProjectsByBrands ?? [])
        } else {
          updateActiveList(FilterEnum.PROJECTS, data ? data[userProfileKey] : [])
        }
      },
      skip: !triggerProjectsQuery || !isActive
    }
  )

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const { data: tasks, loading: tasksDataLoading } = useQuery<any>(requestTaskByProfile, {
    variables: {
      workCode: activeUser?.altairNumber,
      projectIds: activeFilters?.projects?.map((project) => project.id)
    },
    onError: (error) => {
      gplError(error, projectsError)
    },
    onCompleted: (data) => {
      updateActiveList(
        FilterEnum.TASKS,
        (
          data[responseTaskByProfile] as Array<{
            id: string
            name: string
            tasks: Array<{ taskId: string; taskName: string }>
          }>
        ).map((project) => ({
          id: project.id,
          name: project.name,
          subItems: project.tasks?.map((task) => ({ id: task.taskId, name: task.taskName })) ?? []
        })) ?? []
      )
    },
    skip: !triggerTasksQuery || !isActive
  })

  const {
    error: brandsError,
    loading: brandsDataLoading,
    data: brandsData
  } = useQuery<{ getBrands: Brand[] }>(BRANDS_QUERY, {
    variables: { params: brandIds },
    onError: (error) => {
      gplError(error, brandsError)
    },
    onCompleted: (data) => {
      updateActiveList(
        FilterEnum.BRANDS,
        data?.getBrands.map((brand) => ({ id: brand.brandId, name: brand.brandName })) ?? []
      )
    },
    skip: !triggerBrandsQuery || brandIds.length === 0 || !isActive
  })

  const [savePlaceholdersMutation] = useMutation(PLACEHOLDER_SAVE_MUTATION, {
    onCompleted: () => {
      applyFilters()
    },
    onError: (error) => {
      showAlertVar({ message: error.message, show: true })
      gplError(error, error)
    }
  })

  const updateActiveList = (key: string, value: LabelItem[] | LabelItemWithSubItems[]) => {
    setActiveList((prev) => {
      const newMap = new Map(prev)
      newMap.set(key, value)
      return newMap
    })
  }

  const setTaskStates = (_param: boolean) => {
    updateActiveList(
      FilterEnum.TASK_STATES,
      Object.entries(TaskStates).map(([key, value]) => ({ id: key, name: value }))
    )
  }

  const handleFocus = (): void => {
    setIsActive(true)
  }

  const handleTyping = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!activeFilter) return

    setInputText(() => event.target.value)

    if (!activeFilter) {
      return
    }

    if (event.target.value.length < 3) {
      if (activeFilter.id === FilterEnum.RESOURCES) {
        return
      }
    }
    const lowerSearchTearm = event.target.value.toLowerCase()
    if (activeFilter.id === FilterEnum.PROJECTS) {
      handleFilterByProject(lowerSearchTearm)
    } else if (activeFilter.id === FilterEnum.TASKS) {
      handleFilterByTask(lowerSearchTearm)
    } else if (activeFilter.id === FilterEnum.GROUPS) {
      handleFilterByUserGroup(lowerSearchTearm)
    } else if (activeFilter.id === FilterEnum.PLACEHOLDERS) {
      handleFilterByPlaceholder(lowerSearchTearm)
    } else if (activeFilter.id === FilterEnum.BRANDS) {
      handleFilterByBrands(lowerSearchTearm)
    }
  }

  const handleFilterByProject = (searchTerm: string) => {
    const filteredProjects = projects?.[userProfileKey]?.filter((project) =>
      project.name.toLocaleLowerCase().includes(searchTerm)
    )
    updateActiveList(FilterEnum.PROJECTS, filteredProjects ?? [])
  }

  const handleFilterByUserGroup = (searchTerm: string) => {
    const filteredUserGroups =
      usersGroupsData?.usersGroups.filter((userGroup) => userGroup.name.toLocaleLowerCase().includes(searchTerm)) ?? []
    updateActiveList(
      FilterEnum.GROUPS,
      filteredUserGroups.map((userGroup) => ({ id: userGroup.id.toString(), name: userGroup.name })) ?? []
    )
  }

  const handleFilterByPlaceholder = (searchTerm: string) => {
    if (searchTerm.length < 3) {
      updateActiveList(
        FilterEnum.PLACEHOLDERS,
        placeholdersData?.placeholders.items.map((placeholder) => ({ id: placeholder.id, name: placeholder.name })) ??
          []
      )
      return
    }
    const filteredPlaceholders =
      placeholdersData?.placeholders.items.filter((placeholder) =>
        placeholder.name.toLocaleLowerCase().includes(searchTerm)
      ) ?? []

    updateActiveList(
      FilterEnum.PLACEHOLDERS,
      filteredPlaceholders.map((placeholder) => ({ id: placeholder.id, name: placeholder.name })) ?? []
    )
  }

  const handleFilterByBrands = (searchTerm: string) => {
    if (searchTerm.length < 3) {
      updateActiveList(
        FilterEnum.BRANDS,
        brandsData?.getBrands.map((brand) => ({ id: brand.brandId, name: brand.brandName })) ?? []
      )
      return
    }
    const filteredBrands =
      brandsData?.getBrands.filter((brand) => brand.brandName.toLocaleLowerCase().includes(searchTerm)) ?? []

    updateActiveList(
      FilterEnum.BRANDS,
      filteredBrands.map((brand) => ({ id: brand.brandId, name: brand.brandName })) ?? []
    )
  }

  const handleFilterByTask = (searchTerm: string) => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const filteredTasks = tasks[responseTaskByProfile]?.filter((project: { tasks: any[] }) =>
      project.tasks.some((task) => task.taskName.toLocaleLowerCase().includes(searchTerm))
    )
    updateActiveList(
      FilterEnum.TASKS,
      // eslint-disable-next-line @typescript-eslint/no-unsafe-argument, @typescript-eslint/no-explicit-any
      filteredTasks.map((project: { id: any; name: any; tasks: any[] }) => ({
        id: project.id,
        name: project.name,
        subItems: project.tasks
          .filter((task) => {
            return task.taskName.toLocaleLowerCase().includes(searchTerm)
          })
          .map((task) => ({ id: task.taskId, name: task.taskName }))
      }))
    )
  }

  const handleFilterClick = (index: number) => {
    setActiveFilter(availableFilters[index])

    const filterId = availableFilters[index].id.toString()
    const triggerQueryMap: Record<string, React.Dispatch<React.SetStateAction<boolean>>> = {
      usersGroups: setTriggerUsersGroupsQuery,
      // Setup here more filters triggered by user selection
      projects: setTriggerProjectsQuery,
      tasks: setTriggerTasksQuery,
      brands: () => {
        setTriggerBrandsQuery(true)
        setTriggerProjectsQuery(true)
      },
      resources: setTriggerResourcesQuery,
      taskStates: () => {
        setTaskStates(true)
      },
      placeholders: () => {
        setTriggerPlaceholdersFiltersQuery(true)
      }
    }

    if (triggerQueryMap[filterId]) {
      triggerQueryMap[filterId](true)
    }

    ;((inputRef.current as unknown as HTMLElement)?.firstChild as HTMLElement).focus()
  }

  const resetFilterSelection = () => {
    setActiveFilter(null)
    removeTextInput()
    setTriggerUsersGroupsQuery(false)
    setTriggerBrandsQuery(false)
    setTriggerProjectsQuery(false)
    setTriggerResourcesQuery(false)
    setTriggerTasksQuery(false)
    setTriggerPlaceholdersFiltersQuery(false)
  }

  const getSelectAllItems = (category: string) => {
    const allItems: LabelItem[] | LabelItemWithSubItems[] = activeList.get(category) ?? []

    // Check if items have subItems and return an array of all subItems
    const result = allItems.flatMap((item: LabelItem | LabelItemWithSubItems) =>
      'subItems' in item ? item.subItems : item
    )

    return result
  }

  const removeTextInput = () => {
    setInputText('')
  }

  const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      setIsActive(false)
      resetFilterSelection()
    }
  }

  const applyFilters = () => {
    /**
     * * This function is called when the user clicks on the "Apply Filters" button.
     * * Gets the ids from each entity in the activeFilters object and creates a new object with the ids.
     * * The new object is then sent to the server.
     * * The object is created by iterating over the activeFilters object and getting the ids from each entity.
     */
    const cleanData = Object.entries(activeFilters).reduce<Record<string, string[]>>((acc, [key, value]) => {
      const ids = value.map((item) => item.id)
      acc[key] = ids
      return acc
    }, {})
    selectedFiltersVar(cleanData)
    selectedPlaceholderOrgStructureFilterVar(placeholdersFiltersFormData)
    setIsActive(false)
    setExpandPlaceholderActivated(false)
    if (activeFilters && Object.keys(activeFilters).length > 0) {
      setInputText('')
    }
  }

  const toggleExpandPlaceholders = () => {
    const expandPlaceholderFilter = availableFilters.find((filter) => filter.id === FilterEnum.EXPAND_PLACEHOLDERS)
    setActiveFilter(expandPlaceholderFilter ?? null)
    setExpandPlaceholderActivated(true)
  }

  // This function should handle saving the placeholders settings
  const handleSavePlaceholders = (placeholderSettingsFormValues: PlaceholdersSettingsPayload) => {
    const { businessUnit, location, costCenter, jobrole } = placeholderSettingsFormValues
    const body = {
      userLoggedInExternalId: activeUser?.altairNumber,
      businessUnit: [...new Set(businessUnit?.map((item) => `${item.agencyCode} : ${item.agencyName}`) ?? [])],
      location: [...new Set(location?.map((item) => item) ?? [])],
      costCenter: [...new Set(costCenter?.map((item) => `${item.costCenterCode} : ${item.costCenterName}`) ?? [])],
      jobrole: [...new Set(jobrole?.map((item) => `${item.workCode} : ${item.workCodeName}`) ?? [])]
    }
    savePlaceholdersMutation({
      variables: {
        params: {
          ...body
        }
      }
    })
      .then(() => {
        renderKeyVar(Date.now())
        setExpandPlaceholderActivated(true)
      })
      .catch((error) => {
        console.error(error)
      })
  }

  // This function should handle resetting the placeholders settings to defaults
  const handleResetPlaceholders = () => {
    const defaultPayload = {
      userLoggedInExternalId: activeUser?.altairNumber,
      businessUnit: [],
      location: [],
      costCenter: [],
      jobrole: []
    }
    savePlaceholdersMutation({
      variables: {
        params: {
          ...defaultPayload
        }
      }
    })
      .then(() => {
        renderKeyVar(Date.now())
        setExpandPlaceholderActivated(true)
      })
      .catch((error) => {
        console.error(error)
      })
  }

  useEffect(() => {
    if (savePlaceHolders) {
      handleSavePlaceholders(placeholderSettingsPayloadVar())
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [savePlaceHolders])

  useEffect(() => {
    if (resetPlaceholders) {
      handleResetPlaceholders()
      placeholderSettingsResetVar(false) // Reset the trigger
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [resetPlaceholders])

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown)
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('keydown', handleKeyDown)
      document.removeEventListener('mousedown', handleClickOutside)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  useEffect(() => {
    setIsListLoading(
      resourcesDataLoading ||
        usersGroupsDataLoading ||
        projectsDataLoading ||
        tasksDataLoading ||
        brandsDataLoading ||
        placeholdersDataLoading
    )
  }, [
    resourcesDataLoading,
    usersGroupsDataLoading,
    projectsDataLoading,
    tasksDataLoading,
    brandsDataLoading,
    placeholdersDataLoading
  ])

  useEffect(() => {
    const filtersFound = Object.values(activeFilters).some((filterArray) => filterArray.length > 0)
    const selectedFilter = selectedFilters
    const selectedFiltersFound =
      selectedFilter && Object.values(selectedFilter).some((filterArray) => filterArray.length > 0)
    const placeholdersBusinessUnitsSelected = (placeholdersFiltersFormData.businessUnits ?? []).length > 0
    const selectedPlaceholderOrgStructureFilter = !!(
      selectedPlaceholdersOrgStructureFilters &&
      (selectedPlaceholdersOrgStructureFilters.businessUnits ?? []).length > 0
    )

    if (
      placeholdersBusinessUnitsSelected ||
      selectedPlaceholderOrgStructureFilter ||
      filtersFound ||
      selectedFiltersFound
    ) {
      setDisableApplyFilters(false)
    } else {
      setDisableApplyFilters(true)
    }
  }, [activeFilters, selectedFilters, selectedPlaceholdersOrgStructureFilters, placeholdersFiltersFormData])

  return (
    <Box sx={{ display: 'flex', width: '100%', flexDirection: 'column' }}>
      {isActive && (
        <Box
          sx={{
            position: 'absolute',
            left: 0,
            top: 0,
            width: '100vw',
            height: '100vh',
            zIndex: 9,
            background: 'black',
            opacity: 0
          }}
          onClick={() => {
            setIsActive(false)
          }}
        ></Box>
      )}
      <Paper
        component="div"
        sx={{
          display: 'flex',
          alignItems: 'left',
          width: '100%',
          height: '40px',
          boxShadow: 'none',
          zIndex: 10,
          border: `1px solid ${palette.secondary.main}`,
          borderRadius: '0.35rem'
        }}
        ref={searchInputRef}
        id="search-input"
      >
        <Box sx={{ display: 'flex', width: '100%', position: 'relative', zIndex: 10 }}>
          <ChipList />
          <Box
            sx={{
              display: 'flex',
              width: '100%',
              height: '40px',
              borderRadius: '80px',
              zIndex: 100
            }}
          >
            <InputBase
              onClick={handleFocus}
              onChange={handleTyping}
              className={`${'filter-input'}`}
              sx={{ ml: 2, flex: 1, zIndex: 10, caretColor: activeFilter ? 'text.primary' : 'transparent' }}
              placeholder="Search for resources, projects, tasks..."
              ref={inputRef}
              inputProps={{
                // eslint-disable-next-line @typescript-eslint/naming-convention
                'aria-label': 'Search for resources, projects, tasks...',
                ref: inputRef,
                sx: {
                  // eslint-disable-next-line @typescript-eslint/naming-convention
                  '&::placeholder': { color: 'text.secondary', opacity: 1 }
                }
              }}
              value={inputText}
            />

            <SearchIcon sx={{ m: '8px' }} aria-label="search" />
          </Box>

          {isActive &&
            typeof window !== 'undefined' &&
            createPortal(
              <>
                <Box
                  sx={{
                    position: 'absolute',
                    left: 0,
                    top: 0,
                    width: '100vw',
                    height: '100vh',
                    zIndex: 1,
                    background: 'black',
                    opacity: 0
                  }}
                  onClick={() => {
                    setIsActive(false)
                  }}
                ></Box>

                <Box
                  sx={{
                    borderRadius: '0.5rem',
                    top: '4.625rem',
                    background: 'white',
                    position: 'absolute',

                    left: `${(searchInputRef.current?.offsetLeft ?? 0) + 10}px`,

                    zIndex: 100,
                    width: `${searchInputRef.current?.clientWidth ?? 400}px`,
                    boxShadow: '0px 4px 30px 0px rgba(0, 0, 0, 0.2)'
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center'
                    }}
                    autoFocus={false}
                  >
                    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: '100%' }}>
                      {!activeFilter && (
                        <Typography sx={{ margin: ' 24px 0px' }} variant="subtitle2">
                          <b>Choose a Category</b>
                        </Typography>
                      )}

                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          flexWrap: 'wrap',
                          width: !activeFilter ? '80%' : '100%',
                          justifyContent: !activeFilter ? 'center' : 'left',
                          padding: '0.5rem 0 0.2rem'
                        }}
                      >
                        {activeFilter && (
                          <Box sx={{ margin: '0' }}>
                            {activeFilter && (
                              <IconButton onClick={resetFilterSelection}>
                                <SimpleArrowIcon width="20" height="20" rotation="-90" />
                              </IconButton>
                            )}
                          </Box>
                        )}
                        {activeFilter?.id === FilterEnum.EXPAND_PLACEHOLDERS && (
                          <PlaceholdersSettingsBtn toggleExpandPlaceholders={toggleExpandPlaceholders} />
                        )}

                        {availableFilters.map(
                          (filter, index) =>
                            filter.id !== FilterEnum.EXPAND_PLACEHOLDERS &&
                            (!activeFilter || activeFilter.id === filter.id) && (
                              <>
                                <Box key={filter.id}>
                                  <Chip
                                    sx={{
                                      margin: '0.25rem',
                                      padding: '1.5rem 0.5rem',
                                      fontSize: '0.85rem',
                                      fontStyle: 'normal',
                                      fontWeight: '400',
                                      lineHeight: '1.25rem',
                                      borderRadius: '2rem',
                                      display:
                                        userProfile !== 'resourceManager' && filter.id === 'usersGroups'
                                          ? 'none'
                                          : 'flex'
                                    }}
                                    id={filter.id}
                                    icon={<Image src={filter.icon} alt="A filter icon" />}
                                    label={filter.label}
                                    onClick={() => {
                                      handleFilterClick(index)
                                    }}
                                  />
                                </Box>
                                {activeFilter && (
                                  <SelectAllDropdown items={getSelectAllItems(filter.id) ?? []} category={filter.id} />
                                )}
                              </>
                            )
                        )}
                      </Box>
                    </Box>
                    {!activeFilter && userProfile === UserProfile.RESOURCE_MANAGER && (
                      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                        <Box sx={{ marginBottom: '0.2rem' }}>or</Box>
                        <PlaceholdersSettingsBtn toggleExpandPlaceholders={toggleExpandPlaceholders} />
                      </Box>
                    )}

                    {activeFilter && activeFilter.id !== FilterEnum.EXPAND_PLACEHOLDERS && (
                      <Grid width={'100%'}>
                        <Box sx={{ display: 'flex', width: '100%' }}>
                          {activeFilter.id === 'placeholders' && (
                            <Box sx={{ width: '40%' }}>
                              <PlaceholdersFilter
                                setPlaceholdersFiltersFormData={setPlaceholdersFiltersFormData}
                                placeholdersFiltersFormData={placeholdersFiltersFormData}
                              />
                            </Box>
                          )}
                          <Box sx={{ width: activeFilter.id === 'placeholders' ? '60%' : '100%' }}>
                            <FilterList
                              items={activeList.get(activeFilter.id) ?? []}
                              category={activeFilter.id}
                              isLoading={isListLoading}
                            />
                          </Box>
                        </Box>
                      </Grid>
                    )}

                    {activeFilter?.id === FilterEnum.EXPAND_PLACEHOLDERS && <PlaceholdersSettings />}

                    {!activeFilter && (
                      <>
                        <Box sx={{ marginTop: '1rem' }}>
                          <FilterIllustrationIcon width="250" height="250" />
                        </Box>

                        <Typography sx={{ padding: '1rem' }} variant="subtitle2">
                          <b>Refine Your Search with Filters and Categories</b>
                        </Typography>

                        <Typography sx={{ width: '85%' }} textAlign={'center'} variant="body1">
                          Use Filters and Categories to tailor your search. Select &apos;Brand&apos; to see tasks
                          associated with a specific brand, or choose &apos;Project&apos; to view tasks related to a
                          particular project. Click to explore.
                        </Typography>
                      </>
                    )}
                  </Box>

                  <Divider sx={{ width: '100%' }} variant="middle" />
                  <FilterFooter
                    buttonLabel="Apply Filters"
                    enableFilter={disableApplyFilters && !expandPlaceholderActivated}
                    filterAction={applyFilters}
                    isExpandPlaceholder={activeFilter?.id === FilterEnum.EXPAND_PLACEHOLDERS}
                    expandPlaceholdersBackAction={resetFilterSelection}
                  />
                </Box>
              </>,
              document.body
            )}
        </Box>
      </Paper>
    </Box>
  )
}

export default FilterSearch
