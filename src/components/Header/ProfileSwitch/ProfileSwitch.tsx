import { useState, useEffect, memo } from 'react'
import { useReactiveVar } from '@apollo/client'
import { useRouter } from 'next/router'

// GraphQL
import { showAuditTrailVar, userProfileVar, userNameVar, userProfilesVar } from '@gql/client/localState'
import apolloClientInit from '@gql/client/apolloClientInit'
import USER_INFO_QUERY from '@gql/queries/getUserInfo.query'

// UI & Icons
import { Box, Avatar, Typography, ButtonBase, IconButton, Menu, palette } from 'lion-ui'
import { ArrowBack, ChevronRight } from '@mui/icons-material'
import ProfileHeaderIcon from '@components/Icons/ProfileHeaderIcon/ProfileHeaderIcon'
import AuditTrailIcon from '@components/Icons/AuditTrailIcon/AuditTrailIcon'
import SwitchProfilesIcon from '@components/Icons/SwitchProfilesIcon/SwitchProfileIcon'
import ProfileSelectedIcon from '@components/Icons/ProfileSelectedIcon/ProfileSelectedIcon'

// Constants & Utils
import { UserProfile } from '@constants/userProfile'
import { camelToTitle, capitalizeString, toCamelCase } from '@utils/strings'
import { getInitials, normalizeRoles } from '@utils/user'
import type { ProfileOption } from '@typeDefs/Profile'

const ProfileSwitch = () => {
  const [showProfileView, setShowProfileView] = useState(false)
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const router = useRouter()

  // Use reactive variables instead of local state
  const profiles = useReactiveVar(userProfilesVar)
  const userName = useReactiveVar(userNameVar)
  const selectedRole = useReactiveVar(userProfileVar)

  const open = Boolean(anchorEl)
  const isAuditTrailOpen = useReactiveVar(showAuditTrailVar)

  useEffect(() => {
    const fetchUserInfo = async () => {
      // Initialize with default profile if not set
      if (!selectedRole) {
        userProfileVar('lightUser')
      }

      // Check for query parameters first (from Workfront redirect)
      const { userName: queryUserName, businessRole: queryBusinessRole } = router.query

      if (queryUserName && queryBusinessRole) {
        console.log('ProfileSwitch: Using data from query parameters')
        const roles =
          typeof queryBusinessRole === 'string' ? normalizeRoles(queryBusinessRole.split(',')) : normalizeRoles([])

        userNameVar(queryUserName as string)
        userProfilesVar(roles)

        if (!selectedRole) {
          userProfileVar('lightUser')
        }
        return
      }

      // If userNameVar and userProfilesVar are already set, don't call getUserInfo
      if (userName && profiles.length > 0) {
        console.log('ProfileSwitch: User data already available, skipping fetch')
        return
      }

      // Only fetch if we don't have user data and no query params
      try {
        const { data } = await apolloClientInit.query({
          query: USER_INFO_QUERY,
          fetchPolicy: 'network-only'
        })

        const rawRoles = data.getUserInfo.businessRole
        const roles = Array.isArray(rawRoles) ? normalizeRoles(rawRoles as string[]) : normalizeRoles([])
        const fetchedUserName = String(data.getUserInfo.userName)

        userProfilesVar(roles)
        userNameVar(fetchedUserName)

        if (!selectedRole) {
          userProfileVar('lightUser')
        }
      } catch (err) {
        console.error('Error fetching user info:', err)
      }
    }

    void fetchUserInfo()
  }, [selectedRole, userName, profiles.length, router.query])

  const toggleAuditTrail = () => {
    showAuditTrailVar(!isAuditTrailOpen)
    closeMenu()
  }

  const handleProfileClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const closeMenu = () => {
    setAnchorEl(null)
    setShowProfileView(false)
  }

  const handleProfileSwitch = async (role: string) => {
    closeMenu()
    const camelCaseRole = toCamelCase(role)
    userProfileVar(camelCaseRole)
  }

  const profileOptions: ProfileOption[] = [
    {
      icon: SwitchProfilesIcon,
      label: 'Switch Profiles',
      action: () => {
        setShowProfileView(true)
      },
      showChevron: true
    },
    ...(selectedRole !== UserProfile.LIGHT_USER
      ? [
          {
            icon: AuditTrailIcon,
            label: 'Audit Trail',
            action: toggleAuditTrail,
            showChevron: false
          }
        ]
      : [])
  ]

  const renderProfileOption = ({ icon: Icon, label, action, showChevron }: ProfileOption) => (
    <ButtonBase
      key={label}
      sx={{
        width: '100%',
        height: '40px',
        display: 'grid',
        gridTemplateColumns: '1fr 5fr 1fr',
        alignItems: 'center',
        justifyContent: 'flex-start',
        borderRadius: '8px'
      }}
      onClick={action}
    >
      <Box>{<Icon height="20" width="20" fillColor="currentColor" />}</Box>
      <Typography variant="BodyMMedium" sx={{ textAlign: 'left', ml: 2 }}>
        {label}
      </Typography>
      {showChevron && <ChevronRight />}
    </ButtonBase>
  )

  const renderProfileList = () => {
    if (profiles.length === 0) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '60px' }}>
          <Typography variant="BodyMRegular" color="text.secondary">
            Loading profiles...
          </Typography>
        </Box>
      )
    }

    return profiles.map((role) => {
      return (
        <ButtonBase
          key={role}
          sx={{
            height: '30px',
            justifyContent: 'space-between',
            flexDirection: 'row',
            borderRadius: '8px',
            paddingLeft: '10px',
            paddingRight: '20px'
          }}
          onClick={() => {
            void handleProfileSwitch(role)
          }}
        >
          <Typography variant="BodyMMedium">{role}</Typography>
          {camelToTitle(selectedRole || 'lightUser') === role && <ProfileSelectedIcon height="24px" width="24px" />}
        </ButtonBase>
      )
    })
  }

  return (
    <>
      <ButtonBase
        id="profile-switch-button"
        aria-controls={open ? 'profile-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        onClick={handleProfileClick}
        sx={{ borderRadius: '50%', height: 40, width: 40 }}
      >
        <ProfileHeaderIcon />
      </ButtonBase>

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={closeMenu}
        slotProps={{
          paper: {
            sx: {
              width: 250,
              height: 189,
              overflow: 'hidden',
              border: '1px solid #E0E0E0',
              filter: 'drop-shadow(0px 4px 24px rgba(0, 0, 0, 0.1))',
              borderRadius: '8px',
              backgroundColor: palette.common.white
            }
          }
        }}
      >
        <Box
          sx={{
            display: 'flex',
            width: 800,
            transition: 'transform 0.4s ease-in-out',
            transform: showProfileView ? 'translateX(-250px)' : 'translateX(0)'
          }}
        >
          {/* Default View */}
          <Box sx={{ width: 250, p: 2, boxSizing: 'border-box', display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Avatar sx={{ width: 40, height: 40, backgroundColor: palette['100-Accent-1'].main }}>
                <Typography variant="BodyMBold" color="#1077D6">
                  {getInitials(userName || 'User')}
                </Typography>
              </Avatar>
              <Box sx={{ ml: 2 }}>
                <div>
                  <Typography variant="BodyLRegular">{capitalizeString(userName || 'User')}</Typography>
                </div>
                <div>
                  <Typography variant="BodyMRegular" color="text.secondary">
                    {camelToTitle(selectedRole || 'lightUser')}
                  </Typography>
                </div>
              </Box>
            </Box>

            <Box sx={{ border: '0.5px solid #E0E0E0', mb: 2 }} />
            <Box>{profileOptions.map(renderProfileOption)}</Box>
          </Box>

          {/* Switch Profiles View */}
          <Box sx={{ width: 250, p: 2, boxSizing: 'border-box', display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <IconButton
                onClick={() => {
                  setShowProfileView(false)
                }}
              >
                <ArrowBack />
              </IconButton>
              <Typography variant="BodyLRegular" sx={{ ml: 3 }}>
                Switch Profiles
              </Typography>
            </Box>
            <Box sx={{ border: '0.5px solid #E0E0E0', mb: 2 }} />
            {renderProfileList()}
          </Box>
        </Box>
      </Menu>
    </>
  )
}

export default memo(ProfileSwitch)
