// External library imports
import {
  Autocomplete,
  Box,
  Button,
  Checkbox,
  Chip,
  CircularProgress,
  Divider,
  FormLabel,
  palette,
  <PERSON>Field,
  Toolt<PERSON>,
  Typography
} from 'lion-ui'

// Local file imports
import type PlaceholdersFilterProps from './PlaceholdersFilter.props'
import { useState } from 'react'
import { useQuery } from '@apollo/client'
import { type Agency } from '@typeDefs/Agency'
import {
  AGENCY_LOCATIONS_QUERY,
  COST_CENTERS_QUERY,
  // ORG_STRUCTURE_AGENCIES_COST_CENTERS_QUERY,
  SEARCH_RESOURCE_AGENCY_QUERY
} from '@gql/queries/resources.query'
import { gplError } from '@utils/gplError'
import { debounce } from 'lodash'
import { type CostCenter } from '@typeDefs/CostCenter'

import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank'
import CheckBoxIcon from '@mui/icons-material/CheckBox'

const icon = <CheckBoxOutlineBlankIcon fontSize="small" />
const checkedIcon = <CheckBoxIcon fontSize="small" />

const PlaceholdersFilter = (props: PlaceholdersFilterProps) => {
  const [agencySearchTerm, setAgencySearchTerm] = useState<string>('')
  const [agencyInputValue, setAgencyInputValue] = useState<string>('')
  const [locationSearchTerm, setLocationSearchTerm] = useState<string>('')
  const [costCenterSearchTerm, setCostCenterSearchTerm] = useState<string>('')
  // const [getLocations, setGetLocations] = useState<boolean>(true)

  const debouncedSetSearchTerm = debounce((value: string) => {
    setAgencySearchTerm(value)
  }, 300)

  const handleBusinessUnitChange = (value: Agency[] | null) => {
    props.setPlaceholdersFiltersFormData((prev) => ({
      ...prev,
      businessUnits: value,
      locations: null,
      costCenters: null
    }))
    setLocationSearchTerm('')
  }

  const handleCostCenterChange = (value: CostCenter[] | null) => {
    props.setPlaceholdersFiltersFormData((prev) => ({
      ...prev,
      costCenters: value
    }))
    // const isLocationEmpty =
    //   props.placeholdersFiltersFormData.locations === null || props.placeholdersFiltersFormData.locations.length === 0
    // setGetLocations(!isLocationEmpty)
  }

  const {
    data: agencyData,
    error: agencyError,
    loading: agencyLoading
  } = useQuery<{ searchAgencyOrgStructure: Agency[] }>(SEARCH_RESOURCE_AGENCY_QUERY, {
    variables: {
      searchTerm: agencySearchTerm
    },
    onError: (error) => {
      gplError(error, agencyError)
    },
    skip: agencySearchTerm.length < 3
  })

  const {
    data: locationData,
    error: locationError,
    loading: locationLoading
  } = useQuery<{ getLocationsByAgenciesOrgStructure: string[] }>(AGENCY_LOCATIONS_QUERY, {
    variables: {
      agencyCodes: props.placeholdersFiltersFormData?.businessUnits?.map((bu) => bu.agencyCode) ?? []
    },
    onError: (error) => {
      gplError(error, locationError)
    },
    skip:
      !props.placeholdersFiltersFormData.businessUnits || props.placeholdersFiltersFormData.businessUnits.length === 0
  })

  const {
    data: costCentersData,
    error: costCentersError,
    loading: costCentersLoading
  } = useQuery<{ getCostCentersByLocationsOrgStructure: CostCenter[] }>(COST_CENTERS_QUERY, {
    variables: {
      agencyCodes: props.placeholdersFiltersFormData.businessUnits?.map((bu) => bu.agencyCode) ?? [],
      cities: props.placeholdersFiltersFormData.locations
    },
    onError: (error) => {
      gplError(error, costCentersError)
    },
    skip:
      !props.placeholdersFiltersFormData.businessUnits || props.placeholdersFiltersFormData.businessUnits.length === 0
  })

  /**
   * 
   *  This commented logic is for fetching locations based on Agency and cost centers
   * Needs more testing and validation
   * 
  const businessUnits = props.placeholdersFiltersFormData.businessUnits ?? []
  const costCenters = props.placeholdersFiltersFormData.costCenters ?? []

  const params = businessUnits.flatMap((bu) =>
    costCenters.map((cc) => ({
      agencyCode: bu.agencyCode,
      costCenterCode: cc.costCenterCode
    }))
  )

  const { error: locationsError } = useQuery(ORG_STRUCTURE_AGENCIES_COST_CENTERS_QUERY, {
    variables: {
      params
    },
    onCompleted: (data) => {
      console.log('Locations data:', data)
      // const locations = data.getOrgStructureByAgencyCodesCostCenterCodes?.map(
      //   (item: { locationName: string }) => item.locationName
      // )

      // console.log('Locations:', locations)
      // props.setPlaceholdersFiltersFormData((prev) => ({
      //   ...prev,
      //   locations: locations ?? []
      // }))
    },
    onError: (error) => {
      gplError(error, locationsError)
    },
    skip: getLocations
  })

   */

  const handleClearFilters = () => {
    props.setPlaceholdersFiltersFormData({
      businessUnits: null,
      locations: null,
      costCenters: null
    })
    setAgencySearchTerm('')
    setAgencyInputValue('')
    setCostCenterSearchTerm('')
    setLocationSearchTerm('')
    // setGetLocations(true)
  }

  const totalSelectedFilters =
    (props.placeholdersFiltersFormData.businessUnits?.length ?? 0) +
    (props.placeholdersFiltersFormData.locations?.length ?? 0) +
    (props.placeholdersFiltersFormData.costCenters?.length ?? 0)

  return (
    <Box
      sx={{
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'start',
        alignContent: 'center',
        alignItems: 'center',
        borderRight: `1px solid ${palette.secondary.main}`
      }}
    >
      <Box
        sx={{
          width: '100%',
          backgroundColor: palette.grey[50],
          padding: '1rem',
          borderBottom: `1px solid ${palette.secondary.main}`
        }}
      >
        <Typography variant="h3">Filters</Typography>
        <Typography>{totalSelectedFilters} Selected</Typography>
      </Box>

      <Box sx={{ width: '100%', padding: '0.75rem 1rem' }}>
        <FormLabel
          sx={{
            fontWeight: 'bold',
            color: palette.primary.dark,
            padding: '0.7rem 0',
            fontSize: '0.875rem'
          }}
          htmlFor="businessUnit"
        >
          Agency
        </FormLabel>
        <Autocomplete
          multiple
          disableCloseOnSelect
          sx={{ marginTop: '0.5rem' }}
          fullWidth
          disablePortal
          id="businessUnit"
          data-testid="businessUnit"
          options={[...(agencyData?.searchAgencyOrgStructure ?? [])]}
          getOptionLabel={(option) => `${option?.agencyCode}: ${option?.agencyName}`}
          renderInput={(params) => (
            <TextField
              {...params}
              placeholder="Search for a Agency..."
              InputProps={{
                ...params.InputProps,
                endAdornment: (
                  <>
                    {agencyLoading ? <CircularProgress size={20} sx={{ color: '#999', marginRight: '8px' }} /> : null}
                    {params.InputProps.endAdornment}
                  </>
                )
              }}
            />
          )}
          size="small"
          isOptionEqualToValue={(option, value) => option.agencyCode === value.agencyCode}
          value={props.placeholdersFiltersFormData.businessUnits ?? []}
          inputValue={agencyInputValue}
          onInputChange={(_e, newInputValue, reason) => {
            if (reason === 'input') {
              setAgencyInputValue(newInputValue)
              debouncedSetSearchTerm(newInputValue)
            }
          }}
          onChange={(_e, newValue) => {
            handleBusinessUnitChange(newValue)
          }}
          noOptionsText={agencySearchTerm.length < 3 ? 'Type at least 3 characters' : ''}
          loading={agencyLoading}
          renderOption={(props, option, { selected }) => {
            const { key, ...optionProps } = props
            return (
              <li key={key} {...optionProps}>
                <Checkbox icon={icon} checkedIcon={checkedIcon} style={{ marginRight: 8 }} checked={selected} />
                {option.agencyCode}: {option.agencyName}
              </li>
            )
          }}
          renderTags={(selected, getTagProps) => {
            const additionalCount = selected.length - 1
            const tagProps = getTagProps({ index: 0 })

            return [
              // eslint-disable-next-line react/jsx-key
              <Chip {...tagProps} label={`${selected[0]?.agencyCode}: ${selected[0]?.agencyName}`} />,
              ...(additionalCount > 0
                ? [
                    <Tooltip
                      key="more"
                      title={selected
                        .slice(1)
                        .map((o) => `${o.agencyCode}: ${o.agencyName}`)
                        .join(', ')}
                    >
                      <Chip {...getTagProps({ index: 1 })} label={`+${additionalCount}`} onDelete={undefined} />
                    </Tooltip>
                  ]
                : [])
            ]
          }}
          onClose={() => {
            // Reset the search term when the dropdown is closed
            setAgencySearchTerm('')
            setAgencyInputValue('')
          }}
        />
      </Box>

      <Box sx={{ width: '100%', padding: '0.75rem 1rem' }}>
        <Typography variant="h3" sx={{ fontSize: '0.875rem', fontWeight: 'bold' }}>
          Location
        </Typography>
        <Autocomplete
          multiple
          disableCloseOnSelect
          sx={{ marginTop: '0.5rem' }}
          fullWidth
          disablePortal
          id="location"
          data-testid="location"
          options={[...(locationData?.getLocationsByAgenciesOrgStructure ?? [])]}
          getOptionLabel={(option) => `${option}`}
          renderInput={(params) => (
            <TextField
              {...params}
              placeholder="Search for a location..."
              InputProps={{
                ...params.InputProps,
                endAdornment: (
                  <>
                    {locationLoading ? <CircularProgress size={20} sx={{ color: '#999', marginRight: '8px' }} /> : null}
                    {params.InputProps.endAdornment}
                  </>
                )
              }}
            />
          )}
          size="small"
          isOptionEqualToValue={(option, value) => option === value}
          value={props.placeholdersFiltersFormData.locations ?? []}
          inputValue={locationSearchTerm}
          onInputChange={(_e, newInputValue, reason) => {
            if (reason === 'input') {
              setLocationSearchTerm(newInputValue)
            }
          }}
          onChange={(_e, newValue) => {
            props.setPlaceholdersFiltersFormData((prev) => ({
              ...prev,
              locations: newValue
            }))
          }}
          noOptionsText={locationSearchTerm.length < 3 ? 'Type at least 3 characters' : 'No locations found'}
          loading={costCentersLoading}
          renderOption={(props, option, { selected }) => {
            const { key, ...optionProps } = props

            if (option === 'No locations found' || option === 'Type at least 3 characters') {
              return (
                <li key={key} {...optionProps}>
                  <Typography variant="body2" color="textSecondary">
                    {option}
                  </Typography>
                </li>
              )
            }

            return (
              <li key={key} {...optionProps}>
                <Checkbox icon={icon} checkedIcon={checkedIcon} style={{ marginRight: 8 }} checked={selected} />
                {option}
              </li>
            )
          }}
          filterOptions={(options, state) => {
            if (state.inputValue.length === 0) {
              return options
            }
            if (state.inputValue.length < 3) {
              return ['Type at least 3 characters']
            }
            const filtered = options.filter((option) => option.toLowerCase().includes(state.inputValue.toLowerCase()))
            return filtered.length === 0 ? ['No locations found'] : filtered
          }}
          renderTags={(selected, getTagProps) => {
            const additionalCount = selected.length - 1
            const tagProps = getTagProps({ index: 0 })

            return [
              // eslint-disable-next-line react/jsx-key
              <Chip {...tagProps} label={`${selected[0]}`} />,
              ...(additionalCount > 0
                ? [
                    <Tooltip
                      key="more"
                      title={selected
                        .slice(1)
                        .map((o) => `${o}`)
                        .join(', ')}
                    >
                      <Chip {...getTagProps({ index: 1 })} label={`+${additionalCount}`} onDelete={undefined} />
                    </Tooltip>
                  ]
                : [])
            ]
          }}
          onClose={() => {
            // Reset the search term when the dropdown is closed
            setLocationSearchTerm('')
          }}
        />
      </Box>

      <Box sx={{ width: '100%', padding: '0.75rem 1rem' }}>
        <FormLabel
          sx={{
            fontWeight: 'bold',
            color: palette.primary.dark,
            padding: '0.7rem 0',
            fontSize: '0.875rem'
          }}
          htmlFor="costCenter"
        >
          Cost Center
        </FormLabel>
        <Autocomplete
          multiple
          disableCloseOnSelect
          sx={{ marginTop: '0.5rem' }}
          fullWidth
          disablePortal
          id="costCenter"
          data-testid="costCenter"
          options={[...(costCentersData?.getCostCentersByLocationsOrgStructure ?? [])]}
          getOptionLabel={(option) => `${option?.costCenterCode}: ${option?.costCenterName}`}
          renderInput={(params) => (
            <TextField
              {...params}
              placeholder="Search for a cost center..."
              InputProps={{
                ...params.InputProps,
                endAdornment: (
                  <>
                    {costCentersLoading ? (
                      <CircularProgress size={20} sx={{ color: '#999', marginRight: '8px' }} />
                    ) : null}
                    {params.InputProps.endAdornment}
                  </>
                )
              }}
            />
          )}
          size="small"
          isOptionEqualToValue={(option, value) => option.costCenterCode === value.costCenterCode}
          value={props.placeholdersFiltersFormData.costCenters ?? []}
          inputValue={costCenterSearchTerm}
          onInputChange={(_e, newInputValue, reason) => {
            if (reason === 'input') {
              setCostCenterSearchTerm(newInputValue)
            }
          }}
          onChange={(_e, newValue) => {
            handleCostCenterChange(newValue)
          }}
          noOptionsText={costCenterSearchTerm.length < 3 ? 'Type at least 3 characters' : 'No cost centers found'}
          loading={costCentersLoading}
          renderOption={(props, option, { selected }) => {
            const { key, ...optionProps } = props
            if (
              option.costCenterName === 'No cost centers found' ||
              option.costCenterName === 'Type at least 3 characters'
            ) {
              return (
                <li key={key} {...optionProps}>
                  <Typography variant="body2" color="textSecondary">
                    {option.costCenterName}
                  </Typography>
                </li>
              )
            }
            return (
              <li key={key} {...optionProps}>
                <Checkbox icon={icon} checkedIcon={checkedIcon} style={{ marginRight: 8 }} checked={selected} />
                {option.costCenterCode}: {option.costCenterName}
              </li>
            )
          }}
          filterOptions={(options, state) => {
            if (state.inputValue.length === 0) {
              return options
            }
            if (state.inputValue.length < 3) {
              return [{ costCenterCode: '', costCenterName: 'Type at least 3 characters' }]
            }
            const filtered = options.filter((option) => {
              const lowerCaseInput = state.inputValue.toLowerCase()
              const searchValue = option.costCenterCode.toLowerCase() + ' ' + option.costCenterName.toLowerCase()
              return searchValue.includes(lowerCaseInput)
            })
            return filtered.length === 0 ? [{ costCenterCode: '', costCenterName: 'No cost centers found' }] : filtered
          }}
          renderTags={(selected, getTagProps) => {
            const additionalCount = selected.length - 1
            const tagProps = getTagProps({ index: 0 })

            return [
              // eslint-disable-next-line react/jsx-key
              <Chip {...tagProps} label={`${selected[0]?.costCenterCode}: ${selected[0]?.costCenterName}`} />,
              ...(additionalCount > 0
                ? [
                    <Tooltip
                      key="more"
                      title={selected
                        .slice(1)
                        .map((o) => `${o.costCenterCode}: ${o.costCenterName}`)
                        .join(', ')}
                    >
                      <Chip {...getTagProps({ index: 1 })} label={`+${additionalCount}`} onDelete={undefined} />
                    </Tooltip>
                  ]
                : [])
            ]
          }}
          onClose={() => {
            // Reset the search term when the dropdown is closed
            setCostCenterSearchTerm('')
          }}
        />
      </Box>

      <Divider sx={{ width: '100%' }} />

      <Box sx={{ width: '100%', padding: '0.75rem 1rem' }}>
        <Button
          sx={{
            backgroundColor: palette.common.white,
            gap: '0.5rem',
            borderColor: palette.secondary.main,
            fontWeight: 'bold'
          }}
          variant="outlined"
          onClick={handleClearFilters}
          fullWidth
          data-testid="Calendar.Header.Navigation.Day.Today.Button"
        >
          Clear
        </Button>
      </Box>
    </Box>
  )
}

export default PlaceholdersFilter
