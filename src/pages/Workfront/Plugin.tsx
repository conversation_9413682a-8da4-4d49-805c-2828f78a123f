import type { UIXConnection } from '@typeDefs/Adobe'

import apolloClientWorkfront from '@gql/client/apolloClientWorkfront'
import { GET_USER_BY_LLID, WORKFRONT_USER_QUERY } from '@gql/queries/workfront.query'
import { wflionLoginIDVar } from '@gql/client/localState'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import { workfrontConfig } from '@utils/workfront'
import { debugWorkfrontConfig } from '@utils/workfront-debug'

// eslint-disable-next-line @typescript-eslint/naming-convention
function PluginComponent() {
  const [conn, setConn] = useState<UIXConnection | null>(null)
  const router = useRouter()

  useEffect(() => {
    // Run debug utility
    debugWorkfrontConfig()

    const iife = async (): Promise<void> => {
      try {
        console.log('Plugin Component - Initializing with config:', {
          id: workfrontConfig.id,
          uixId: workfrontConfig.uixId,
          instanceId: workfrontConfig.instanceId,
          isInIframe: workfrontConfig.isInIframe,
          hostname: typeof window !== 'undefined' ? window.location.hostname : 'undefined',
          url: window.location.href
        })

        const attach = await import('@adobe/uix-guest').then((module) => module.attach)
        console.log('Plugin Component - Attempting to attach with UIX ID:', workfrontConfig.uixId)

        const connection = await attach({ id: workfrontConfig.uixId })
        console.log('Plugin Component - Successfully attached, connection:', connection)

        setConn(connection as unknown as UIXConnection)
      } catch (e) {
        console.error('Plugin Component - Error from connection:', e)
        console.error('Plugin Component - Failed with config:', workfrontConfig)
      }
    }

    void iife()
  }, [])

  useEffect(() => {
    if (!conn) return

    const fetchWFUser = async (): Promise<void> => {
      try {
        const token = conn.sharedContext?.get('auth')?.imsToken
        const userID = conn.sharedContext?.get('user')?.ID
        const hostname = conn.sharedContext?.get('hostname')
        const objCode = conn.sharedContext?.get('objCode')
        const objID = conn.sharedContext?.get('objID')

        console.log('objCode', objCode)
        console.log('objID', objID)
        console.log('conn', conn)

        const { data: workfrontData } = await apolloClientWorkfront.query({
          query: WORKFRONT_USER_QUERY,
          variables: {
            hostname,
            imsToken: token,
            userId: userID
          },
          fetchPolicy: 'network-only'
        })

        const lionLoginID = workfrontData.getWorkfrontUser.lionLoginID as string

        wflionLoginIDVar(lionLoginID)

        const { data: userData } = await apolloClientWorkfront.query({
          query: GET_USER_BY_LLID,
          variables: {
            llid: lionLoginID
          },
          fetchPolicy: 'network-only'
        })

        const { employeeCode, businessRole, userName } = userData.getUserByLLID

        // Pass all data as query parameters since we're redirecting
        const params = new URLSearchParams({
          employeeCode,
          userName,
          businessRole: Array.isArray(businessRole) ? businessRole.join(',') : businessRole
        })

        window.location.href = `/?${params.toString()}`
      } catch (error) {
        console.log('Error', error)
      }
    }

    void fetchWFUser()
  }, [conn, router])

  return <div>Loading...</div>
}

export default PluginComponent
