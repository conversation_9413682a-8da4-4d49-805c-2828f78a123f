import type React from 'react'
import { render } from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import { ThemeProvider, createTheme } from '@mui/material/styles'
import TooltipContent from '@components/Calendar/ToolTipContent/TooltipContent'
import { UserProfile } from '@constants/userProfile'
import { userProfileVar } from '@gql/client/localState'
import { MockedProvider } from '@apollo/client/testing'

const theme = createTheme()

const renderWithTheme = (ui: React.ReactElement) => {
  return render(
    <ThemeProvider theme={theme}>
      <MockedProvider>{ui}</MockedProvider>
    </ThemeProvider>
  )
}

describe('TooltipContent', () => {
  const mockAssignment = {
    id: '123',
    hoursPerDay: 8,
    startDate: '2024-11-01',
    dueDate: '2024-12-31',
    assignmentState: 'Active',
    altairJobNumber: '12345',
    taskState: 'Active',
    projectName: 'Test Project Name',
    taskName: 'Test Task Name',
    projectId: '123',
    state: 'Active',
    userId: '10082922',
    taskId: '456',
    activeNonWorkingDays: [],
    x: 0,
    y: 0,
    width: 0,
    height: 0,
    totalDays: 0,
    externalLink: '',
    isPlaceholder: true,
    projectManagerName: 'John Doe',
    assignmentNotes: '',
    taskNotes: '',
    taskStartDate: '2024-11-01',
    taskDueDate: '2024-12-31',
    projectAgencyCode: 'agency-2',
    projectBrandCode: 'brand-2',
    assignmentIcaStatus: 'Needs Approval',
    projectAgencyName: 'Agency 2',
    projectIntegrationId: 'project-integration-id-1',
    userIntegrationId: 'user-integration-id-1',
    costCenterName: 'Cost Center 1',
    locationName: 'Location 1',
    agencyName: 'Agency 1',
    agencyCode: 'agency-1',
    costCenterCode: 'cost-center-1',
    blockedByTimesheet: false
  }

  test('renders correctly for light user', () => {
    userProfileVar(UserProfile.LIGHT_USER)
    const { getByText } = renderWithTheme(<TooltipContent assignment={mockAssignment} />)

    expect(getByText(`${mockAssignment.projectManagerName}`)).toBeInTheDocument()
    expect(getByText(`${mockAssignment.projectName}`)).toBeInTheDocument()
    expect(getByText(`${mockAssignment.taskName}`)).toBeInTheDocument()
    expect(getByText(`${mockAssignment.taskState}`)).toBeInTheDocument()
    expect(getByText(`${mockAssignment.altairJobNumber ? mockAssignment.altairJobNumber : 'N/A'}`)).toBeInTheDocument()
    expect(getByText(`${mockAssignment.agencyCode} : ${mockAssignment.agencyName}`)).toBeInTheDocument()
    expect(getByText(`${mockAssignment.locationName}`)).toBeInTheDocument()
    expect(getByText(`${mockAssignment.costCenterName}`)).toBeInTheDocument()
    expect(() => getByText('ICA Status: ')).toThrow()
  })

  test('renders correctly for Project Manager user with ICA status', () => {
    userProfileVar(UserProfile.PROJECT_MANAGER)
    const { getByText } = renderWithTheme(<TooltipContent assignment={mockAssignment} />)

    expect(getByText(`${mockAssignment.projectManagerName}`)).toBeInTheDocument()
    expect(getByText(`${mockAssignment.projectName}`)).toBeInTheDocument()
    expect(getByText(`${mockAssignment.taskName}`)).toBeInTheDocument()
    expect(getByText(`${mockAssignment.taskState}`)).toBeInTheDocument()
    expect(getByText(`${mockAssignment.altairJobNumber ? mockAssignment.altairJobNumber : 'N/A'}`)).toBeInTheDocument()
    expect(getByText(`${mockAssignment.assignmentIcaStatus}`)).toBeInTheDocument()
    expect(getByText(`${mockAssignment.agencyCode} : ${mockAssignment.agencyName}`)).toBeInTheDocument()
  })

  test('renders correctly for Resource Manager user with ICA status', () => {
    userProfileVar(UserProfile.RESOURCE_MANAGER)
    const { getByText } = renderWithTheme(<TooltipContent assignment={mockAssignment} />)

    expect(getByText(`${mockAssignment.projectManagerName}`)).toBeInTheDocument()
    expect(getByText(`${mockAssignment.projectName}`)).toBeInTheDocument()
    expect(getByText(`${mockAssignment.taskName}`)).toBeInTheDocument()
    expect(getByText(`${mockAssignment.taskState}`)).toBeInTheDocument()
    expect(getByText(`${mockAssignment.altairJobNumber ? mockAssignment.altairJobNumber : 'N/A'}`)).toBeInTheDocument()
    expect(getByText(`${mockAssignment.assignmentIcaStatus}`)).toBeInTheDocument()
    expect(getByText(`${mockAssignment.agencyCode} : ${mockAssignment.agencyName}`)).toBeInTheDocument()
  })
})
