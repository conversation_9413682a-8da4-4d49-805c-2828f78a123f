import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'

/* eslint-disable testing-library/no-node-access */
/* eslint-disable @typescript-eslint/no-non-null-assertion */

import ProfileSwitch from '@components/Header/ProfileSwitch/ProfileSwitch'

// Mock Next.js router
const mockPush = jest.fn()
const mockRouter = {
  push: mockPush,
  pathname: '/',
  query: {},
  asPath: '/',
  route: '/',
  back: jest.fn(),
  forward: jest.fn(),
  reload: jest.fn(),
  replace: jest.fn(),
  prefetch: jest.fn(),
  beforePopState: jest.fn(),
  events: {
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn()
  },
  isFallback: false,
  isLocaleDomain: false,
  isReady: true,
  isPreview: false
}

jest.mock('next/router', () => ({
  useRouter: () => mockRouter
}))

// Create mock functions
const mockUserProfileVar = jest.fn()
const mockUserNameVar = jest.fn()
const mockUserProfilesVar = jest.fn()
const mockShowAuditTrailVar = jest.fn()

mockUserProfileVar.mockReturnValue('projectManager')
mockUserNameVar.mockReturnValue('Test User')
mockUserProfilesVar.mockReturnValue(['Project Manager', 'Resource Manager'])
mockShowAuditTrailVar.mockReturnValue(false)

jest.mock('@gql/client/localState', () => ({
  userProfileVar: jest.fn().mockImplementation((...args: [string?]) => {
    if (args.length > 0) {
      return mockUserProfileVar(...args)
    }
    return mockUserProfileVar()
  }),
  userNameVar: jest.fn().mockImplementation((...args: [string?]) => {
    if (args.length > 0) {
      return mockUserNameVar(...args)
    }
    return mockUserNameVar()
  }),
  userProfilesVar: jest.fn().mockImplementation((...args: [string[]?]) => {
    if (args.length > 0) {
      return mockUserProfilesVar(...args)
    }
    return mockUserProfilesVar()
  }),
  showAuditTrailVar: jest.fn().mockImplementation((...args: [boolean?]) => {
    if (args.length > 0) {
      return mockShowAuditTrailVar(...args)
    }
    return mockShowAuditTrailVar()
  })
}))

jest.mock('@apollo/client', () => ({
  useReactiveVar: jest.fn((varFn) => {
    return varFn()
  }),
  gql: (template: TemplateStringsArray) => template.raw[0]
}))

jest.mock('@gql/client/apolloClientInit', () => ({
  query: jest.fn().mockResolvedValue({
    data: {
      getUserInfo: {
        userName: 'Test user',
        businessRole: ['Project Manager', 'Resource Manager'],
        employeeCode: '12345'
      }
    }
  })
}))

jest.mock('@utils/user', () => ({
  normalizeRoles: jest.fn().mockReturnValue(['Project Manager', 'Resource Manager']),
  camelToRole: jest.fn().mockImplementation((str: string) => {
    if (str === 'projectManager') return 'Project Manager'
    if (str === 'resourceManager') return 'Resource Manager'
    if (str === 'lightUser') return 'Light User'
    return str
  }),
  toCamelCase: jest.fn().mockImplementation((str: string) => {
    if (str === 'Project Manager') return 'projectManager'
    if (str === 'Resource Manager') return 'resourceManager'
    if (str === 'Light User') return 'lightUser'
    return str
  }),
  getInitials: jest.fn().mockImplementation((name: string) => name.charAt(0).toUpperCase())
}))

jest.mock('@utils/strings', () => ({
  camelToRole: jest.fn().mockImplementation((str: string) => {
    if (str === 'projectManager') return 'Project Manager'
    if (str === 'resourceManager') return 'Resource Manager'
    if (str === 'lightUser') return 'Light User'
    return str
  }),
  capitalizeString: jest.fn().mockImplementation((str: string) => str),
  toCamelCase: jest.fn().mockImplementation((str: string) => {
    if (str === 'Project Manager') return 'projectManager'
    if (str === 'Resource Manager') return 'resourceManager'
    if (str === 'Light User') return 'lightUser'
    return str
  }),
  camelToTitle: jest.fn().mockImplementation((str: string) => {
    if (str === 'projectManager') return 'Project Manager'
    if (str === 'resourceManager') return 'Resource Manager'
    if (str === 'lightUser') return 'Light User'

    const temp = str
      .replace(/([A-Z])/g, ' $1')
      .replace(/\s+/g, ' ')
      .trim()
    return temp.charAt(0).toUpperCase() + temp.slice(1)
  })
}))

describe('ProfileSwitch Component', () => {
  beforeEach(() => {
    // Reset reactive variable mocks
    mockUserProfileVar.mockReturnValue('projectManager')
    mockUserNameVar.mockReturnValue('Test User')
    mockUserProfilesVar.mockReturnValue(['Project Manager', 'Resource Manager'])
    mockShowAuditTrailVar.mockReturnValue(false)

    const apolloClientMock = jest.requireMock('@gql/client/apolloClientInit')
    apolloClientMock.query.mockResolvedValue({
      data: {
        getUserInfo: {
          userName: 'Test user',
          businessRole: ['Project Manager', 'Resource Manager'],
          employeeCode: '12345'
        }
      }
    })

    jest.clearAllMocks()
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  test('loads user info and sets initial profile', async () => {
    render(<ProfileSwitch />)

    await waitFor(() => {
      const button = document.querySelector('#profile-switch-button')
      expect(button).toBeInTheDocument()
    })

    const profileButton = document.querySelector('#profile-switch-button')!
    fireEvent.click(profileButton)

    await waitFor(() => {
      const menuItems = screen.getAllByRole('button')
      expect(menuItems.length).toBeGreaterThan(1)
    })

    expect(screen.getByText('Test User')).toBeInTheDocument()

    const userInfoSection = screen.getByText('Test User').closest('div')?.parentElement
    const roleText = userInfoSection?.querySelector('.MuiTypography-BodyMRegular')
    expect(roleText?.textContent).toBe('Project Manager')
  })

  test('switches profiles correctly', async () => {
    render(<ProfileSwitch />)

    await waitFor(() => {
      expect(document.querySelector('#profile-switch-button')).toBeInTheDocument()
    })

    const profileButton = document.querySelector('#profile-switch-button')!
    fireEvent.click(profileButton)

    const switchProfilesButtons = screen.getAllByText('Switch Profiles')
    fireEvent.click(switchProfilesButtons[0])

    const resourceManagerOption = await screen.findByText('Resource Manager')
    fireEvent.click(resourceManagerOption)

    expect(mockUserProfileVar).toHaveBeenCalledWith('resourceManager')

    mockUserProfileVar.mockReturnValue('resourceManager')

    fireEvent.click(profileButton)

    await screen.findByText('Test User')

    const userInfoSection = screen.getByText('Test User').closest('div')?.parentElement
    const roleText = userInfoSection?.querySelector('.MuiTypography-BodyMRegular')
    expect(roleText?.textContent).toBe('Resource Manager')
  })

  test('shows audit trail option for non-light users', async () => {
    mockUserProfileVar.mockReturnValue('projectManager')

    render(<ProfileSwitch />)

    await waitFor(() => {
      const button = document.querySelector('#profile-switch-button')
      expect(button).toBeInTheDocument()
    })

    const profileButton = document.querySelector('#profile-switch-button')!
    fireEvent.click(profileButton)

    await waitFor(() => {
      const menuItems = screen.getAllByRole('button')
      expect(menuItems.length).toBeGreaterThan(1)
    })

    expect(screen.getByText('Audit Trail')).toBeInTheDocument()
  })

  test('hides audit trail option for light users', async () => {
    mockUserProfileVar.mockReturnValue('lightUser')
    mockUserProfilesVar.mockReturnValue(['Light User'])

    jest.requireMock('@gql/client/apolloClientInit').query.mockResolvedValue({
      data: {
        getUserInfo: {
          userName: 'Test user',
          businessRole: ['Light User'],
          employeeCode: '12345'
        }
      }
    })

    render(<ProfileSwitch />)

    await waitFor(() => {
      const button = document.querySelector('#profile-switch-button')
      expect(button).toBeInTheDocument()
    })

    const profileButton = document.querySelector('#profile-switch-button')!
    fireEvent.click(profileButton)

    await waitFor(() => {
      const menuItems = screen.getAllByRole('button')
      expect(menuItems.length).toBeGreaterThan(1)
    })

    expect(screen.queryByText('Audit Trail')).not.toBeInTheDocument()
  })

  test('shows reactive variable values immediately', async () => {
    mockUserNameVar.mockReturnValue('Stored User')
    mockUserProfilesVar.mockReturnValue(['Project Manager', 'Resource Manager'])
    mockUserProfileVar.mockReturnValue('lightUser')

    const apolloClientMock = jest.requireMock('@gql/client/apolloClientInit')
    apolloClientMock.query.mockImplementation(
      async () =>
        await new Promise((resolve) =>
          setTimeout(() => {
            resolve({
              data: {
                getUserInfo: {
                  userName: 'Test user',
                  businessRole: ['Project Manager'],
                  employeeCode: '12345'
                }
              }
            })
          }, 100)
        )
    )

    render(<ProfileSwitch />)

    const button = document.querySelector('#profile-switch-button')
    expect(button).toBeInTheDocument()

    fireEvent.click(button!)

    await waitFor(() => {
      expect(screen.getByText('Stored User')).toBeInTheDocument()
    })

    expect(screen.getByText('Light User')).toBeInTheDocument()

    const switchProfilesButtons = screen.getAllByText('Switch Profiles')
    fireEvent.click(switchProfilesButtons[0])

    await waitFor(() => {
      expect(screen.getByText('Project Manager')).toBeInTheDocument()
    })

    expect(screen.getByText('Resource Manager')).toBeInTheDocument()
  })
})
/* eslint-enable testing-library/no-node-access */
/* eslint-enable @typescript-eslint/no-non-null-assertion */
